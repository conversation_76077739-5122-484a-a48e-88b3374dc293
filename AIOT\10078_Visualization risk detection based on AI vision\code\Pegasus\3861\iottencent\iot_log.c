/*
 * Copyright (c) 2022 HiSilicon (Shanghai) Technologies CO., LIMITED.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <iot_log.h>

static EnIotLogLevel gIoTLogLevel = EN_IOT_LOG_LEVEL_TRACE;
static const char *gIoTLogLevelNames[] = {
    "TRACE",
    "DEBUG",
    "INFO ",
    "WARN ",
    "ERROR",
    "FATAL"
};

int IoTLogLevelSet(EnIotLogLevel level)
{
    int ret = -1;
    if (level < EN_IOT_LOG_LEVEL_MAX) {
        gIoTLogLevel = level;
        ret = 0;
    }
    return ret;
}

EnIotLogLevel IoTLogLevelGet(void)
{
    return gIoTLogLevel;
}

const char *IoTLogLevelGetName(EnIotLogLevel logLevel)
{
    if (logLevel >= EN_IOT_LOG_LEVEL_MAX) {
        return "NULL ";
    } else {
        return gIoTLogLevelNames[logLevel];
    }
}
