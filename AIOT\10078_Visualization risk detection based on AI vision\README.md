*作品简介

首先Taurus端将火焰识别结果通过串口传输给Pegasus端；烟雾传感器将烟雾识别结果通过串口传输给Pegasus端，然后Pegasus将结果显示在OLED屏幕上，有报警信息则显示fire和smoke等于1，没有则显示0；并将结果通过MQTT上传至云端，云端在发送给小程序。

*代码介绍

（1）Taurus端代码

yolov2加resent18为训练代码，用于训练weights模型用于下一步wk模型的转换，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/6.png)

aismaple为检测+通信代码，用作板端的检测和与Pegasus端的通信 ，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/7.png)

（2）Pegasus端代码

interconnection_client用于与Taurus端通过串口进行互联，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/1.png)

iottencent用于与云端进行数据传输，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/2.png)

uart2用于接收烟雾传感器报警信息 ，具体代码结构如下图所示：

 ![](https://gitee.com/lin-qiyang/tu/raw/master/3.png)

oled用于屏幕显示 ，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/4.png)

（3）小程序端代码

   用于实现页面逻辑和页面布局，具体代码结构如下图所示：

![](https://gitee.com/lin-qiyang/tu/raw/master/5.png)
