<view style="display:flex; flex-direction:column; align-items:center;">
  <image style="width:200px; height:60px;" src="../../image/HiSpark.png" mode="cover"></image>
</view>

<view class="body">
  <view style="font-weight: bold;">
    设备信息
  </view>
  <text>\n</text>
  <view class="box">
    <view class="cell">
      <view class="status-left">产品ID</view>
      <view class="status-right">{{productId}}</view>
    </view>
    <view class="cell">
      <view class="status-left">设备名称</view>
      <view class="status-right">{{deviceName}}</view>
    </view>
  </view>

  <text>\n</text>

  <form bindsubmit="">
    <view style="display:flex; flex-direction:row; justify-content: space-between;">
      <view style="font-weight: bold;">
        各风险状态获取
      </view>
      <view>
        <button type="primary" size="mini" bindtap="update">刷新</button>
      </view>
    </view>
   <view class="box">
      <view class="cell">
         <!--<view>检测设备连接状态</view>
        <view>
          <switch name="light" data-item="light" checked="{{stateReported.light}}" bindchange="switchChange"/>
       </view>-->
      </view>
      <view class="cell">
        <view>火焰</view>
         <view>
         <!-- <switch name="motor" data-item="motor" checked="{{stateReported.motor}}" bindchange="switchChange"/>-->
          <view class="right">{{stateReported.motor == 1 ? '有' : '无'}}</view>
        </view> 

</view>
<view class="cell">
        <view class="left">烟雾</view>
        <view class="right">{{stateReported.temperature == 1 ? '有' : '无'}}</view>
      </view> 
      <view class="cell">
        <!-- <view class="left">火焰</view>
       <view class="right">{{stateReported.humidity == 1 ? '有' : '无'}}</view> -->
      </view> 
      <view class="cell">
        <!-- <view class="left">烟雾</view>
        <view class="right">{{stateReported.light_intensity == 1 ? '有' : '无'}}</view> -->
      </view>
    </view>
  </form>
</view>
