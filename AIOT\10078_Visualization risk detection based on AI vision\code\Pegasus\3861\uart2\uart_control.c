/*
 * Copyright (c) 2022 HiSilicon (Shanghai) Technologies CO., LIMITED.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <unistd.h>
#include <string.h>

#include "iot_gpio_ex.h"
#include "ohos_init.h"
#include "cmsis_os2.h"
#include "iot_gpio.h"
#include "iot_uart.h"
#include "hi_uart.h"
#include "iot_watchdog.h"
#include "iot_errno.h"
#include "oled_ssd1306.h"

#define UART_BUFF_SIZE 100
#define U_SLEEP_TIME   100000


void Uart2GpioInit(void)
{
    IoTGpioInit(IOT_IO_NAME_GPIO_11);
    // 设置GPIO0的管脚复用关系为UART1_TX Set the pin reuse relationship of GPIO0 to UART1_ TX
    IoSetFunc(IOT_IO_NAME_GPIO_11, IOT_IO_FUNC_GPIO_11_UART2_TXD);
    IoTGpioInit(IOT_IO_NAME_GPIO_12);
    // 设置GPIO1的管脚复用关系为UART1_RX Set the pin reuse relationship of GPIO1 to UART1_ RX
    IoSetFunc(IOT_IO_NAME_GPIO_12, IOT_IO_FUNC_GPIO_12_UART2_RXD);
}

void Uart2Config(void)
{
    uint32_t ret2;
    /* 初始化UART配置，波特率 9600，数据bit为8,停止位1，奇偶校验为NONE */
    /* Initialize UART configuration, baud rate is 9600, data bit is 8, stop bit is 1, parity is NONE */
    IotUartAttribute uart2_attr = {
        .baudRate = 12800,
        .dataBits = 8,
        .stopBits = 1,
        .parity = 0,
    };
    ret2 = IoTUartInit(HI_UART_IDX_2, &uart2_attr);
    if (ret2 != IOT_SUCCESS) {
        printf("Init Uart1 Falied Error No : %d\n", ret2);
        return;
    }
}

int smoke = 0;
static void UartTask(void)
{
    const char *data = "Hello OpenHarmony !!!\n";

    unsigned char uartReadBuff[UART_BUFF_SIZE] = {0};

    // const char *data = "Hello OpenHarmony !!!\n";
    uint32_t cnt = 0;
    uint32_t len = 0;

    // 对UART2的一些初始化 Some initialization of UART1
    Uart2GpioInit();
    // 对UART2参数的一些配置 Some configurations of UART1 parameters
    Uart2Config();

    while (1) {

        len = IoTUartRead(HI_UART_IDX_2, uartReadBuff, UART_BUFF_SIZE);

        if (len > 0) {
            smoke = 1;
            DisplayVariable1(smoke);
            // 把接收到的数据打印出来
            printf("Uart2: [ %d ] %s \r\n", smoke, uartReadBuff);
            len = 0;
        }

        else
        {
            if (cnt > 25)
            {
                smoke = 0;
                DisplayVariable1(smoke);
                cnt = 0;
            }
            printf("Uart UnRead2 : [ %d  ] \r\n", smoke);
        }

        usleep(U_SLEEP_TIME);
        cnt++; 
    }
}


void UartExampleEntry(void)
{
    osThreadAttr_t attr;
    IoTWatchDogDisable();

    attr.name = "UartTask";
    attr.attr_bits = 0U;
    attr.cb_mem = NULL;
    attr.cb_size = 0U;
    attr.stack_mem = NULL;
    attr.stack_size = 5 * 1024; // 任务栈大小*1024 stack size 5*1024
    attr.priority = osPriorityNormal;

    if (osThreadNew((osThreadFunc_t)UartTask, NULL, &attr) == NULL) {
        printf("[UartTask] Failed to create UartTask!\n");
    }
}

APP_FEATURE_INIT(UartExampleEntry);