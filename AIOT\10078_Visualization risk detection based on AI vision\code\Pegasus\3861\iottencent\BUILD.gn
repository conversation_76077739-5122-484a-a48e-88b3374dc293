# Copyright (c) 2022 HiSilicon (Shanghai) Technologies CO., LIMITED.
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

static_library("appDemoIot") {
  sources = [
    "app_demo_iot.c",
    "cjson_init.c",
    "iot_hmac.c",
    "iot_log.c",
    "iot_main.c",
    "iot_profile.c",
    "iot_sta.c",
  ]

  include_dirs = [
    "./",
    "//utils/native/lite/include",
    "//kernel/liteos_m/kal/cmsis",
    "//base/iot_hardware/peripheral/interfaces/kits",
    "//device/hisilicon/hispark_pegasus/sdk_liteos/third_party/lwip_sack/include/lwip",
    "//third_party/cJSON",
    "//device/hisilicon/hispark_pegasus/sdk_liteos/third_party/mbedtls/include/mbedtls",
    "//foundation/communication/wifi_lite/interfaces/wifiservice",
    "//device/hisilicon/hispark_pegasus/sdk_liteos/third_party/paho.mqtt.c/include/mqtt",
    "//device/hisilicon/hispark_pegasus/sdk_liteos/third_party/libcoap/include/coap2",
  ]

  defines = [ "WITH_LWIP" ]
}
